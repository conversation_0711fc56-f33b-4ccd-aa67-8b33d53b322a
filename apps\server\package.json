{"name": "server", "main": "src/index.ts", "type": "module", "scripts": {"build": "wrangler deploy --dry-run", "check-types": "tsc -b", "compile": "bun build --compile --minify --sourcemap --bytecode ./src/index.ts --outfile server", "dev": "wrangler dev --port=3000", "start": "wrangler dev", "deploy": "wrangler deploy", "cf-typegen": "wrangler types --env-interface CloudflareBindings", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate"}, "dependencies": {"dotenv": "^17.2.1", "zod": "^4.0.2", "@trpc/server": "^11.4.2", "@trpc/client": "^11.4.2", "@hono/trpc-server": "^0.4.0", "hono": "^4.8.2", "drizzle-orm": "^0.44.2", "@libsql/client": "^0.15.9", "better-auth": "^1.3.4"}, "devDependencies": {"tsdown": "^0.12.9", "typescript": "^5.8.2", "drizzle-kit": "^0.31.2", "wrangler": "^4.23.0", "@types/node": "^22.13.11"}}